const dbConnection = require('../database/connection');
const QRCode = require('qrcode');

class Student {
    constructor(data) {
        this.student_id = data.student_id;
        this.lrn = data.lrn;
        this.first_name = data.first_name;
        this.last_name = data.last_name;
        this.grade_level = data.grade_level;
        this.section = data.section;
        this.parent_contact = data.parent_contact;
        this.enrollment_status = data.enrollment_status;
        this.photo_path = data.photo_path;
        this.qr_code_data = data.qr_code_data;
        this.created_at = data.created_at;
        this.updated_at = data.updated_at;
    }

    /**
     * Create a new student
     * @param {Object} studentData - Student data
     * @returns {Promise<Student>}
     */
    static async create(studentData) {
        const { lrn, first_name, last_name, grade_level, section, parent_contact, photo_path } = studentData;
        
        // Generate QR code data
        const qr_code_data = `STUDENT_${lrn}`;
        
        const result = await dbConnection.run(
            `INSERT INTO students (lrn, first_name, last_name, grade_level, section, parent_contact, photo_path, qr_code_data) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
            [lrn, first_name, last_name, grade_level, section, parent_contact, photo_path, qr_code_data]
        );
        
        return await Student.findById(result.lastID);
    }

    /**
     * Find student by ID
     * @param {number} id - Student ID
     * @returns {Promise<Student|null>}
     */
    static async findById(id) {
        const row = await dbConnection.get(
            'SELECT * FROM students WHERE student_id = ?',
            [id]
        );
        
        return row ? new Student(row) : null;
    }

    /**
     * Find student by LRN
     * @param {string} lrn - Learner Reference Number
     * @returns {Promise<Student|null>}
     */
    static async findByLRN(lrn) {
        const row = await dbConnection.get(
            'SELECT * FROM students WHERE lrn = ?',
            [lrn]
        );
        
        return row ? new Student(row) : null;
    }

    /**
     * Find student by QR code data
     * @param {string} qrCodeData - QR code data
     * @returns {Promise<Student|null>}
     */
    static async findByQRCode(qrCodeData) {
        const row = await dbConnection.get(
            'SELECT * FROM students WHERE qr_code_data = ?',
            [qrCodeData]
        );
        
        return row ? new Student(row) : null;
    }

    /**
     * Get all students
     * @param {Object} filters - Optional filters
     * @returns {Promise<Student[]>}
     */
    static async findAll(filters = {}) {
        let sql = 'SELECT * FROM students WHERE 1=1';
        let params = [];
        
        if (filters.grade_level) {
            sql += ' AND grade_level = ?';
            params.push(filters.grade_level);
        }
        
        if (filters.section) {
            sql += ' AND section = ?';
            params.push(filters.section);
        }
        
        if (filters.enrollment_status) {
            sql += ' AND enrollment_status = ?';
            params.push(filters.enrollment_status);
        }
        
        sql += ' ORDER BY last_name, first_name';
        
        const rows = await dbConnection.all(sql, params);
        return rows.map(row => new Student(row));
    }

    /**
     * Update student
     * @param {Object} updateData - Data to update
     * @returns {Promise<Student>}
     */
    async update(updateData) {
        const fields = [];
        const values = [];
        
        for (const [key, value] of Object.entries(updateData)) {
            if (key !== 'student_id' && key !== 'created_at') {
                fields.push(`${key} = ?`);
                values.push(value);
            }
        }
        
        if (fields.length === 0) {
            throw new Error('No valid fields to update');
        }
        
        values.push(this.student_id);
        
        await dbConnection.run(
            `UPDATE students SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE student_id = ?`,
            values
        );
        
        return await Student.findById(this.student_id);
    }

    /**
     * Delete student
     * @returns {Promise<boolean>}
     */
    async delete() {
        const result = await dbConnection.run(
            'DELETE FROM students WHERE student_id = ?',
            [this.student_id]
        );
        
        return result.changes > 0;
    }

    /**
     * Generate QR code image
     * @returns {Promise<string>} Base64 encoded QR code image
     */
    async generateQRCode() {
        try {
            return await QRCode.toDataURL(this.qr_code_data);
        } catch (error) {
            throw new Error('Failed to generate QR code: ' + error.message);
        }
    }

    /**
     * Get full name
     * @returns {string}
     */
    getFullName() {
        return `${this.first_name} ${this.last_name}`;
    }

    /**
     * Convert to JSON
     * @returns {Object}
     */
    toJSON() {
        return {
            student_id: this.student_id,
            lrn: this.lrn,
            first_name: this.first_name,
            last_name: this.last_name,
            full_name: this.getFullName(),
            grade_level: this.grade_level,
            section: this.section,
            parent_contact: this.parent_contact,
            enrollment_status: this.enrollment_status,
            photo_path: this.photo_path,
            qr_code_data: this.qr_code_data,
            created_at: this.created_at,
            updated_at: this.updated_at
        };
    }
}

module.exports = Student;
