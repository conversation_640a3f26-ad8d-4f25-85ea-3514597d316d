const dbConnection = require('../database/connection');
const bcrypt = require('bcrypt');

class User {
    constructor(data) {
        this.user_id = data.user_id;
        this.username = data.username;
        this.password_hash = data.password_hash;
        this.role = data.role;
        this.full_name = data.full_name;
        this.contact_number = data.contact_number;
        this.created_at = data.created_at;
        this.updated_at = data.updated_at;
    }

    /**
     * Create a new user
     * @param {Object} userData - User data
     * @returns {Promise<User>}
     */
    static async create(userData) {
        const { username, password, role, full_name, contact_number } = userData;
        
        // Hash password
        const password_hash = await bcrypt.hash(password, 10);
        
        const result = await dbConnection.run(
            `INSERT INTO users (username, password_hash, role, full_name, contact_number) 
             VALUES (?, ?, ?, ?, ?)`,
            [username, password_hash, role, full_name, contact_number]
        );
        
        return await User.findById(result.lastID);
    }

    /**
     * Find user by ID
     * @param {number} id - User ID
     * @returns {Promise<User|null>}
     */
    static async findById(id) {
        const row = await dbConnection.get(
            'SELECT * FROM users WHERE user_id = ?',
            [id]
        );
        
        return row ? new User(row) : null;
    }

    /**
     * Find user by username
     * @param {string} username - Username
     * @returns {Promise<User|null>}
     */
    static async findByUsername(username) {
        const row = await dbConnection.get(
            'SELECT * FROM users WHERE username = ?',
            [username]
        );
        
        return row ? new User(row) : null;
    }

    /**
     * Get all users
     * @param {string} role - Optional role filter
     * @returns {Promise<User[]>}
     */
    static async findAll(role = null) {
        let sql = 'SELECT * FROM users';
        let params = [];
        
        if (role) {
            sql += ' WHERE role = ?';
            params.push(role);
        }
        
        sql += ' ORDER BY created_at DESC';
        
        const rows = await dbConnection.all(sql, params);
        return rows.map(row => new User(row));
    }

    /**
     * Verify password
     * @param {string} password - Plain text password
     * @returns {Promise<boolean>}
     */
    async verifyPassword(password) {
        return await bcrypt.compare(password, this.password_hash);
    }

    /**
     * Update user
     * @param {Object} updateData - Data to update
     * @returns {Promise<User>}
     */
    async update(updateData) {
        const fields = [];
        const values = [];
        
        for (const [key, value] of Object.entries(updateData)) {
            if (key !== 'user_id' && key !== 'password_hash' && key !== 'created_at') {
                fields.push(`${key} = ?`);
                values.push(value);
            }
        }
        
        if (fields.length === 0) {
            throw new Error('No valid fields to update');
        }
        
        values.push(this.user_id);
        
        await dbConnection.run(
            `UPDATE users SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE user_id = ?`,
            values
        );
        
        return await User.findById(this.user_id);
    }

    /**
     * Delete user
     * @returns {Promise<boolean>}
     */
    async delete() {
        const result = await dbConnection.run(
            'DELETE FROM users WHERE user_id = ?',
            [this.user_id]
        );
        
        return result.changes > 0;
    }

    /**
     * Convert to JSON (exclude password hash)
     * @returns {Object}
     */
    toJSON() {
        const { password_hash, ...userWithoutPassword } = this;
        return userWithoutPassword;
    }
}

module.exports = User;
