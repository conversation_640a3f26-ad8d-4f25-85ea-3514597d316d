const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// Database configuration
const DB_PATH = path.join(__dirname, 'qrsams.db');
const SCHEMA_PATH = path.join(__dirname, 'schema.sql');

class DatabaseConnection {
    constructor() {
        this.db = null;
    }

    /**
     * Initialize database connection
     * @returns {Promise<sqlite3.Database>}
     */
    async connect() {
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(DB_PATH, (err) => {
                if (err) {
                    console.error('Error opening database:', err.message);
                    reject(err);
                } else {
                    console.log('Connected to SQLite database.');
                    // Enable foreign key constraints
                    this.db.run('PRAGMA foreign_keys = ON;', (err) => {
                        if (err) {
                            console.error('Error enabling foreign keys:', err.message);
                            reject(err);
                        } else {
                            resolve(this.db);
                        }
                    });
                }
            });
        });
    }

    /**
     * Initialize database tables from schema file
     * @returns {Promise<void>}
     */
    async initializeTables() {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('Database not connected'));
                return;
            }

            // Read schema file
            fs.readFile(SCHEMA_PATH, 'utf8', (err, schema) => {
                if (err) {
                    console.error('Error reading schema file:', err.message);
                    reject(err);
                    return;
                }

                // Execute schema SQL
                this.db.exec(schema, (err) => {
                    if (err) {
                        console.error('Error executing schema:', err.message);
                        reject(err);
                    } else {
                        console.log('Database tables initialized successfully.');
                        resolve();
                    }
                });
            });
        });
    }

    /**
     * Get database instance
     * @returns {sqlite3.Database}
     */
    getDatabase() {
        return this.db;
    }

    /**
     * Close database connection
     * @returns {Promise<void>}
     */
    async close() {
        return new Promise((resolve, reject) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        console.error('Error closing database:', err.message);
                        reject(err);
                    } else {
                        console.log('Database connection closed.');
                        resolve();
                    }
                });
            } else {
                resolve();
            }
        });
    }

    /**
     * Execute a query with parameters
     * @param {string} sql - SQL query
     * @param {Array} params - Query parameters
     * @returns {Promise<Object>}
     */
    async run(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ 
                        lastID: this.lastID, 
                        changes: this.changes 
                    });
                }
            });
        });
    }

    /**
     * Get a single row
     * @param {string} sql - SQL query
     * @param {Array} params - Query parameters
     * @returns {Promise<Object|undefined>}
     */
    async get(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    /**
     * Get all rows
     * @param {string} sql - SQL query
     * @param {Array} params - Query parameters
     * @returns {Promise<Array>}
     */
    async all(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }
}

// Create singleton instance
const dbConnection = new DatabaseConnection();

module.exports = dbConnection;
