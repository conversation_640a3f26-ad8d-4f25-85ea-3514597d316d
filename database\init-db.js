const dbConnection = require('./connection');
const bcrypt = require('bcrypt');

/**
 * Initialize database with tables and default data
 */
async function initializeDatabase() {
    try {
        console.log('Starting database initialization...');
        
        // Connect to database
        await dbConnection.connect();
        
        // Initialize tables from schema
        await dbConnection.initializeTables();
        
        // Create default admin user
        await createDefaultAdmin();
        
        // Create sample data (optional)
        await createSampleData();
        
        console.log('Database initialization completed successfully!');
        
    } catch (error) {
        console.error('Database initialization failed:', error.message);
        process.exit(1);
    } finally {
        await dbConnection.close();
    }
}

/**
 * Create default admin user
 */
async function createDefaultAdmin() {
    try {
        // Check if admin already exists
        const existingAdmin = await dbConnection.get(
            'SELECT user_id FROM users WHERE username = ?',
            ['admin']
        );
        
        if (existingAdmin) {
            console.log('Default admin user already exists.');
            return;
        }
        
        // Create default admin
        const defaultPassword = 'admin123'; // Change this in production!
        const hashedPassword = await bcrypt.hash(defaultPassword, 10);
        
        await dbConnection.run(
            `INSERT INTO users (username, password_hash, role, full_name, contact_number) 
             VALUES (?, ?, ?, ?, ?)`,
            ['admin', hashedPassword, 'admin', 'System Administrator', '09123456789']
        );
        
        console.log('Default admin user created successfully.');
        console.log('Username: admin');
        console.log('Password: admin123');
        console.log('*** PLEASE CHANGE THE DEFAULT PASSWORD IN PRODUCTION! ***');
        
    } catch (error) {
        console.error('Error creating default admin:', error.message);
        throw error;
    }
}

/**
 * Create sample data for testing (optional)
 */
async function createSampleData() {
    try {
        console.log('Creating sample data...');
        
        // Create sample teacher
        const teacherPassword = await bcrypt.hash('teacher123', 10);
        const teacherResult = await dbConnection.run(
            `INSERT INTO users (username, password_hash, role, full_name, contact_number) 
             VALUES (?, ?, ?, ?, ?)`,
            ['teacher1', teacherPassword, 'teacher', 'John Doe', '09987654321']
        );
        
        const teacherId = teacherResult.lastID;
        
        // Create sample subject
        await dbConnection.run(
            `INSERT INTO subjects (subject_code, subject_name, grade_level, teacher_id) 
             VALUES (?, ?, ?, ?)`,
            ['MATH101', 'Mathematics', 'Grade 7', teacherId]
        );
        
        // Create sample students
        const students = [
            {
                lrn: '123456789012',
                firstName: 'Alice',
                lastName: 'Johnson',
                gradeLevel: 'Grade 7',
                section: 'Section A',
                parentContact: '09111111111',
                qrCode: 'STUDENT_123456789012'
            },
            {
                lrn: '123456789013',
                firstName: 'Bob',
                lastName: 'Smith',
                gradeLevel: 'Grade 7',
                section: 'Section A',
                parentContact: '09222222222',
                qrCode: 'STUDENT_123456789013'
            }
        ];
        
        for (const student of students) {
            await dbConnection.run(
                `INSERT INTO students (lrn, first_name, last_name, grade_level, section, parent_contact, qr_code_data) 
                 VALUES (?, ?, ?, ?, ?, ?, ?)`,
                [student.lrn, student.firstName, student.lastName, student.gradeLevel, 
                 student.section, student.parentContact, student.qrCode]
            );
        }
        
        console.log('Sample data created successfully.');
        console.log('Sample teacher - Username: teacher1, Password: teacher123');
        
    } catch (error) {
        console.error('Error creating sample data:', error.message);
        // Don't throw error for sample data - it's optional
    }
}

// Run initialization if this file is executed directly
if (require.main === module) {
    initializeDatabase();
}

module.exports = {
    initializeDatabase,
    createDefaultAdmin,
    createSampleData
};
