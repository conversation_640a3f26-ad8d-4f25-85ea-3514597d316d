-- QR-Code Based Student Attendance and Monitoring System Database Schema
-- SQLite Database Schema with proper indexing

-- Users table for system authentication
CREATE TABLE IF NOT EXISTS users (
    user_id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL DEFAULT 'teacher' CHECK (role IN ('admin', 'teacher', 'staff')),
    full_name VARCHAR(100) NOT NULL,
    contact_number VARCHAR(20),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Students table for student information
CREATE TABLE IF NOT EXISTS students (
    student_id INTEGER PRIMARY KEY AUTOINCREMENT,
    lrn VARCHAR(20) UNIQUE NOT NULL, -- Learner Reference Number
    first_name VARCHAR(50) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    grade_level VARCHAR(10) NOT NULL,
    section VARCHAR(20) NOT NULL,
    parent_contact VARCHAR(20) NOT NULL,
    enrollment_status VARCHAR(20) DEFAULT 'active' CHECK (enrollment_status IN ('active', 'inactive', 'transferred', 'graduated')),
    photo_path VARCHAR(255),
    qr_code_data TEXT UNIQUE NOT NULL, -- QR code content for student identification
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Subjects table for academic subjects
CREATE TABLE IF NOT EXISTS subjects (
    subject_id INTEGER PRIMARY KEY AUTOINCREMENT,
    subject_code VARCHAR(20) UNIQUE NOT NULL,
    subject_name VARCHAR(100) NOT NULL,
    grade_level VARCHAR(10) NOT NULL,
    teacher_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (teacher_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Class sessions table for attendance sessions
CREATE TABLE IF NOT EXISTS class_sessions (
    session_id INTEGER PRIMARY KEY AUTOINCREMENT,
    subject_id INTEGER NOT NULL,
    teacher_id INTEGER NOT NULL,
    room_number VARCHAR(20),
    date_time DATETIME NOT NULL,
    qr_code_data TEXT UNIQUE NOT NULL, -- QR code for this specific session
    qr_expiry DATETIME NOT NULL, -- When the QR code expires
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'completed')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES subjects(subject_id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Attendance records table
CREATE TABLE IF NOT EXISTS attendance (
    attendance_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    session_id INTEGER NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'present' CHECK (status IN ('present', 'late', 'absent')),
    teacher_id INTEGER NOT NULL,
    remarks TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES class_sessions(session_id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE(student_id, session_id) -- Prevent duplicate attendance records
);

-- SMS logs table for parent notifications
CREATE TABLE IF NOT EXISTS sms_logs (
    sms_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    parent_contact VARCHAR(20) NOT NULL,
    message_content TEXT NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    delivery_status VARCHAR(20) DEFAULT 'pending' CHECK (delivery_status IN ('pending', 'sent', 'failed', 'delivered')),
    teacher_phone VARCHAR(20),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE
);

-- Indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);

CREATE INDEX IF NOT EXISTS idx_students_lrn ON students(lrn);
CREATE INDEX IF NOT EXISTS idx_students_grade_section ON students(grade_level, section);
CREATE INDEX IF NOT EXISTS idx_students_enrollment_status ON students(enrollment_status);
CREATE INDEX IF NOT EXISTS idx_students_qr_code ON students(qr_code_data);

CREATE INDEX IF NOT EXISTS idx_subjects_code ON subjects(subject_code);
CREATE INDEX IF NOT EXISTS idx_subjects_teacher ON subjects(teacher_id);
CREATE INDEX IF NOT EXISTS idx_subjects_grade ON subjects(grade_level);

CREATE INDEX IF NOT EXISTS idx_sessions_datetime ON class_sessions(date_time);
CREATE INDEX IF NOT EXISTS idx_sessions_teacher ON class_sessions(teacher_id);
CREATE INDEX IF NOT EXISTS idx_sessions_subject ON class_sessions(subject_id);
CREATE INDEX IF NOT EXISTS idx_sessions_status ON class_sessions(status);
CREATE INDEX IF NOT EXISTS idx_sessions_qr_code ON class_sessions(qr_code_data);

CREATE INDEX IF NOT EXISTS idx_attendance_student ON attendance(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_session ON attendance(session_id);
CREATE INDEX IF NOT EXISTS idx_attendance_timestamp ON attendance(timestamp);
CREATE INDEX IF NOT EXISTS idx_attendance_status ON attendance(status);
CREATE INDEX IF NOT EXISTS idx_attendance_teacher ON attendance(teacher_id);

CREATE INDEX IF NOT EXISTS idx_sms_student ON sms_logs(student_id);
CREATE INDEX IF NOT EXISTS idx_sms_timestamp ON sms_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_sms_status ON sms_logs(delivery_status);
