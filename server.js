const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const multer = require('multer');
const moment = require('moment');
const dbConnection = require('./database/connection');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware configuration
app.use(cors({
    origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
    credentials: true
}));

app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

// Static file serving
app.use('/public', express.static(path.join(__dirname, 'public')));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        if (file.fieldname === 'studentPhoto') {
            cb(null, 'uploads/student-photos/');
        } else {
            cb(null, 'uploads/');
        }
    },
    filename: function (req, file, cb) {
        // Generate unique filename with timestamp
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB limit
    },
    fileFilter: function (req, file, cb) {
        // Accept only image files for student photos
        if (file.fieldname === 'studentPhoto') {
            if (file.mimetype.startsWith('image/')) {
                cb(null, true);
            } else {
                cb(new Error('Only image files are allowed for student photos!'), false);
            }
        } else {
            cb(null, true);
        }
    }
});

// Make upload middleware available globally
app.locals.upload = upload;

// Request logging middleware
app.use((req, res, next) => {
    console.log(`${moment().format('YYYY-MM-DD HH:mm:ss')} - ${req.method} ${req.path}`);
    next();
});

// Error handling middleware for multer
app.use((error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
                success: false,
                message: 'File too large. Maximum size is 5MB.'
            });
        }
    }
    next(error);
});

// Routes (to be implemented)
// app.use('/api/auth', require('./routes/auth'));
// app.use('/api/students', require('./routes/students'));
// app.use('/api/subjects', require('./routes/subjects'));
// app.use('/api/sessions', require('./routes/sessions'));
// app.use('/api/attendance', require('./routes/attendance'));
// app.use('/api/reports', require('./routes/reports'));

// Basic route for testing
app.get('/', (req, res) => {
    res.json({
        message: 'QR-Code Based Student Attendance and Monitoring System API',
        version: '1.0.0',
        timestamp: moment().format('YYYY-MM-DD HH:mm:ss')
    });
});

// Health check endpoint
app.get('/health', async (req, res) => {
    try {
        // Check database connection
        const db = dbConnection.getDatabase();
        if (!db) {
            throw new Error('Database not connected');
        }
        
        res.json({
            status: 'healthy',
            database: 'connected',
            timestamp: moment().format('YYYY-MM-DD HH:mm:ss')
        });
    } catch (error) {
        res.status(500).json({
            status: 'unhealthy',
            error: error.message,
            timestamp: moment().format('YYYY-MM-DD HH:mm:ss')
        });
    }
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: 'Route not found',
        path: req.originalUrl
    });
});

// Global error handler
app.use((err, req, res, next) => {
    console.error('Error:', err.stack);
    res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
    });
});

// Initialize database and start server
async function startServer() {
    try {
        // Connect to database
        await dbConnection.connect();
        console.log('Database connected successfully.');
        
        // Start server
        app.listen(PORT, () => {
            console.log(`Server is running on port ${PORT}`);
            console.log(`API URL: http://localhost:${PORT}`);
            console.log(`Health check: http://localhost:${PORT}/health`);
        });
        
    } catch (error) {
        console.error('Failed to start server:', error.message);
        process.exit(1);
    }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\nShutting down server...');
    await dbConnection.close();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\nShutting down server...');
    await dbConnection.close();
    process.exit(0);
});

// Start the server
startServer();

module.exports = app;
